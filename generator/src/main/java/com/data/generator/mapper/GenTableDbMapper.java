package com.data.generator.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.data.common.core.mapper.BaseMapperPlus;
import com.data.generator.domain.GenTable;
import com.data.generator.domain.GenTableColumn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务表数据库查询 数据层
 * 用于查询业务表结构信息，使用动态数据源
 *
 * <AUTHOR> Li
 */
@DS("#header.datasource")
@InterceptorIgnore(dataPermission = "true")
public interface GenTableDbMapper extends BaseMapperPlus<GenTableDbMapper, GenTable, GenTable> {

    /**
     * 查询据库列表
     *
     * @param genTable 查询条件
     * @return 数据库表集合
     */
    Page<GenTable> selectPageDbTableList(@Param("page") Page<GenTable> page, @Param("genTable") GenTable genTable);

    /**
     * 查询据库列表
     *
     * @param tableNames 表名称组
     * @return 数据库表集合
     */
    List<GenTable> selectDbTableListByNames(String[] tableNames);

    /**
     * 查询表名称业务信息
     *
     * @param tableName 表名称
     * @return 业务信息
     */
    GenTable selectTableByName(String tableName);

    /**
     * 根据表名称查询列信息
     *
     * @param tableName 表名称
     * @return 列信息
     */
    List<GenTableColumn> selectDbTableColumnsByName(String tableName);

}
